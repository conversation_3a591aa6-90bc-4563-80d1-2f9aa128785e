{"name": "blog-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "build:static": "npm run generate:static && next build", "generate:static": "node scripts/generate-static.js", "optimize:images": "node scripts/optimize-images.js", "critical:css": "node scripts/critical-css.js", "start": "next start", "lint": "next lint", "deploy": "wrangler pages publish out --project-name=blog-frontend", "test:db": "node scripts/test-database.js", "db:setup": "node scripts/setup-database.js", "db:migrate": "node scripts/migrate.js", "db:test": "node scripts/test-database-connection.js", "db:local": "wrangler d1 execute blog-database --local", "db:remote": "wrangler d1 execute blog-database"}, "dependencies": {"@cloudflare/d1": "^1.4.1", "@cloudflare/workers-types": "^4.20250730.0", "critical": "^7.2.1", "lucide-react": "^0.534.0", "next": "15.4.5", "react": "19.1.0", "react-dom": "19.1.0", "sharp": "^0.34.3", "wrangler": "^4.26.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.5", "tailwindcss": "^4", "typescript": "^5"}}