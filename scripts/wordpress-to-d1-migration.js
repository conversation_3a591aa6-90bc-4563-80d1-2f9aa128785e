#!/usr/bin/env node

/**
 * WordPress to Cloudflare D1 Migration Script
 * 
 * This script migrates data from your WordPress SQL dump to Cloudflare D1 database.
 * It handles the conversion from WordPress schema to your D1 blog schema.
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

const CONFIG = {
  WORDPRESS_SQL_FILE: 'u957990218_GpBKT.zayotech-com.20250727190356.sql',
  OUTPUT_DIR: 'migration-output',
  DATABASE_NAME: 'blog-database',
  DATABASE_ID: '38d675e4-df5b-4c8a-b6f2-490996d8dd89'
};

class WordPressMigrator {
  constructor() {
    this.sqlContent = '';
    this.outputDir = path.join(process.cwd(), CONFIG.OUTPUT_DIR);
    this.data = {
      users: [],
      posts: [],
      categories: [],
      tags: [],
      postCategories: [],
      postTags: [],
      comments: []
    };
  }

  async migrate() {
    console.log('🚀 Starting WordPress to D1 Migration...\n');

    try {
      // Step 1: Read WordPress SQL file
      await this.readWordPressSQL();
      
      // Step 2: Parse WordPress data
      await this.parseWordPressData();
      
      // Step 3: Transform data for D1
      await this.transformDataForD1();
      
      // Step 4: Generate D1 SQL files
      await this.generateD1SQLFiles();
      
      // Step 5: Execute migration
      await this.executeMigration();
      
      console.log('\n🎉 Migration completed successfully!');
      console.log('\n📋 Next steps:');
      console.log('1. Check the migration-output directory for generated SQL files');
      console.log('2. Run "npm run dev" to test your blog with migrated data');
      console.log('3. Your WordPress content is now in Cloudflare D1!');
      
    } catch (error) {
      console.error('❌ Migration failed:', error.message);
      process.exit(1);
    }
  }

  async readWordPressSQL() {
    console.log('📖 Reading WordPress SQL file...');
    
    if (!fs.existsSync(CONFIG.WORDPRESS_SQL_FILE)) {
      throw new Error(`WordPress SQL file not found: ${CONFIG.WORDPRESS_SQL_FILE}`);
    }
    
    this.sqlContent = fs.readFileSync(CONFIG.WORDPRESS_SQL_FILE, 'utf8');
    console.log(`✅ Read ${(this.sqlContent.length / 1024 / 1024).toFixed(2)}MB of SQL data`);
  }

  async parseWordPressData() {
    console.log('🔍 Parsing WordPress data...');
    
    // Parse users
    this.parseUsers();
    
    // Parse categories and tags (terms)
    this.parseTerms();
    
    // Parse posts
    this.parsePosts();
    
    // Parse comments
    this.parseComments();
    
    console.log(`✅ Parsed: ${this.data.users.length} users, ${this.data.posts.length} posts, ${this.data.categories.length} categories, ${this.data.comments.length} comments`);
  }

  parseUsers() {
    const userInsertMatch = this.sqlContent.match(/INSERT INTO `wp_users` VALUES\s*([\s\S]*?);/);
    if (!userInsertMatch) return;

    const userValues = userInsertMatch[1];
    const userRows = this.extractInsertValues(userValues);
    
    userRows.forEach(row => {
      if (row.length >= 10) {
        this.data.users.push({
          id: parseInt(row[0]),
          username: this.cleanString(row[1]),
          password_hash: this.cleanString(row[2]),
          email: this.cleanString(row[4]),
          display_name: this.cleanString(row[9]),
          registered: this.cleanString(row[6])
        });
      }
    });
  }

  parseTerms() {
    // Parse wp_terms
    const termsInsertMatch = this.sqlContent.match(/INSERT INTO `wp_terms` VALUES\s*([\s\S]*?);/);
    if (!termsInsertMatch) return;

    const termValues = termsInsertMatch[1];
    const termRows = this.extractInsertValues(termValues);
    
    // Parse wp_term_taxonomy to distinguish categories from tags
    const taxonomyInsertMatch = this.sqlContent.match(/INSERT INTO `wp_term_taxonomy` VALUES\s*([\s\S]*?);/);
    const taxonomyMap = new Map();
    
    if (taxonomyInsertMatch) {
      const taxonomyValues = taxonomyInsertMatch[1];
      const taxonomyRows = this.extractInsertValues(taxonomyValues);
      
      taxonomyRows.forEach(row => {
        if (row.length >= 4) {
          taxonomyMap.set(parseInt(row[1]), {
            taxonomy: this.cleanString(row[2]),
            count: parseInt(row[4]) || 0
          });
        }
      });
    }
    
    termRows.forEach(row => {
      if (row.length >= 3) {
        const termId = parseInt(row[0]);
        const taxonomy = taxonomyMap.get(termId);
        
        if (taxonomy && taxonomy.taxonomy === 'category') {
          this.data.categories.push({
            id: termId,
            name: this.cleanString(row[1]),
            slug: this.cleanString(row[2])
          });
        } else if (taxonomy && taxonomy.taxonomy === 'post_tag') {
          this.data.tags.push({
            id: termId,
            name: this.cleanString(row[1]),
            slug: this.cleanString(row[2])
          });
        }
      }
    });
  }

  parsePosts() {
    // Find all INSERT INTO wp_posts VALUES statements
    const postsInsertMatches = this.sqlContent.match(/INSERT INTO `wp_posts` VALUES\s*([\s\S]*?)(?=INSERT INTO|$)/g);
    if (!postsInsertMatches) {
      console.log('❌ No wp_posts INSERT statements found');
      return;
    }

    console.log(`📝 Found ${postsInsertMatches.length} wp_posts INSERT statements`);
    let allPostRows = [];

    // Process each INSERT statement
    postsInsertMatches.forEach((insertStatement, index) => {
      const valuesMatch = insertStatement.match(/VALUES\s*([\s\S]*?)(?=INSERT INTO|$)/);
      if (valuesMatch) {
        const postRows = this.extractInsertValues(valuesMatch[1]);
        console.log(`   Statement ${index + 1}: ${postRows.length} rows`);
        allPostRows = allPostRows.concat(postRows);
      }
    });

    console.log(`📊 Total rows to process: ${allPostRows.length}`);

    // Debug: collect all post types and statuses
    const postTypes = new Set();
    const postStatuses = new Set();

    allPostRows.forEach(row => {
      if (row.length >= 24) {
        const postStatus = this.cleanString(row[7]);
        const postType = this.cleanString(row[21]);

        postTypes.add(postType);
        postStatuses.add(postStatus);

        // Only migrate published posts and pages
        if (postStatus === 'publish' && (postType === 'post' || postType === 'page')) {
          this.data.posts.push({
            id: parseInt(row[0]),
            author_id: parseInt(row[1]),
            title: this.cleanString(row[5]),
            content: this.cleanString(row[4]),
            excerpt: this.cleanString(row[6]),
            slug: this.cleanString(row[11]),
            status: 'published',
            post_type: postType,
            published_at: this.convertWordPressDate(this.cleanString(row[2])),
            created_at: this.convertWordPressDate(this.cleanString(row[2])),
            updated_at: this.convertWordPressDate(this.cleanString(row[14])),
            comment_count: parseInt(row[23]) || 0
          });
        }
      }
    });

    console.log(`📊 Post types found: ${Array.from(postTypes).join(', ')}`);
    console.log(`📊 Post statuses found: ${Array.from(postStatuses).join(', ')}`);
    console.log(`✓ Parsed ${this.data.posts.length} posts`);
  }

  parseComments() {
    const commentsInsertMatch = this.sqlContent.match(/INSERT INTO `wp_comments` VALUES\s*([\s\S]*?);/);
    if (!commentsInsertMatch) return;

    const commentValues = commentsInsertMatch[1];
    const commentRows = this.extractInsertValues(commentValues);
    
    commentRows.forEach(row => {
      if (row.length >= 15) {
        const commentApproved = this.cleanString(row[12]);
        
        if (commentApproved === '1') { // Only approved comments
          this.data.comments.push({
            id: parseInt(row[0]),
            post_id: parseInt(row[1]),
            author_name: this.cleanString(row[2]),
            author_email: this.cleanString(row[3]),
            content: this.cleanString(row[5]),
            created_at: this.convertWordPressDate(this.cleanString(row[6])),
            status: 'approved',
            parent_id: parseInt(row[11]) || null
          });
        }
      }
    });
  }

  extractInsertValues(insertString) {
    const rows = [];
    let currentRow = [];
    let inQuotes = false;
    let quoteChar = '';
    let currentValue = '';
    let i = 0;
    
    while (i < insertString.length) {
      const char = insertString[i];
      
      if (!inQuotes && (char === "'" || char === '"')) {
        inQuotes = true;
        quoteChar = char;
      } else if (inQuotes && char === quoteChar) {
        // Check for escaped quote
        if (insertString[i + 1] === quoteChar) {
          currentValue += char;
          i++; // Skip next quote
        } else {
          inQuotes = false;
          quoteChar = '';
        }
      } else if (!inQuotes && char === ',') {
        currentRow.push(currentValue.trim());
        currentValue = '';
      } else if (!inQuotes && char === ')' && insertString[i + 1] === ',') {
        currentRow.push(currentValue.trim());
        rows.push(currentRow);
        currentRow = [];
        currentValue = '';
        i++; // Skip comma
      } else if (!inQuotes && char === ')' && i === insertString.length - 1) {
        currentRow.push(currentValue.trim());
        rows.push(currentRow);
      } else if (char !== '(' || inQuotes) {
        currentValue += char;
      }
      
      i++;
    }
    
    return rows;
  }

  cleanString(str) {
    if (!str || str === 'NULL') return '';
    return str.replace(/^['"]|['"]$/g, '').replace(/''/g, "'").replace(/""/g, '"');
  }

  convertWordPressDate(dateStr) {
    if (!dateStr || dateStr === '0000-00-00 00:00:00') return null;
    return dateStr.replace(' ', 'T') + 'Z';
  }

  async transformDataForD1() {
    console.log('🔄 Transforming data for D1...');
    
    // Transform users - map WordPress roles to D1 roles
    this.data.users = this.data.users.map(user => ({
      ...user,
      role: user.id === 1 ? 'admin' : 'author', // First user is admin
      status: 'active',
      email_verified: true,
      bio: `Migrated from WordPress user: ${user.username}`
    }));
    
    // Transform posts - ensure all required fields
    this.data.posts = this.data.posts.map(post => ({
      ...post,
      language: 'en', // Default to English
      view_count: 0,
      meta_title: post.title,
      meta_description: post.excerpt || post.title
    }));
    
    console.log('✅ Data transformation completed');
  }

  async generateD1SQLFiles() {
    console.log('📝 Generating D1 SQL files...');

    // Create output directory
    if (!fs.existsSync(this.outputDir)) {
      fs.mkdirSync(this.outputDir, { recursive: true });
    }

    // Generate users SQL
    await this.generateUsersSQL();

    // Generate categories SQL
    await this.generateCategoriesSQL();

    // Generate tags SQL
    await this.generateTagsSQL();

    // Generate posts SQL
    await this.generatePostsSQL();

    // Generate comments SQL
    await this.generateCommentsSQL();

    // Generate relationships SQL
    await this.generateRelationshipsSQL();

    // Generate migration script
    await this.generateMigrationScript();

    console.log('✅ SQL files generated successfully');
  }

  async generateUsersSQL() {
    const sqlLines = ['-- Migrated WordPress Users'];

    this.data.users.forEach(user => {
      // Use INSERT OR REPLACE to handle existing users
      const sql = `INSERT OR REPLACE INTO users (id, username, email, password_hash, display_name, role, status, email_verified, bio, created_at) VALUES (${user.id}, '${this.escapeSql(user.username)}', '${this.escapeSql(user.email)}', '${this.escapeSql(user.password_hash)}', '${this.escapeSql(user.display_name)}', '${user.role}', '${user.status}', ${user.email_verified}, '${this.escapeSql(user.bio)}', '${user.registered}');`;
      sqlLines.push(sql);
    });

    fs.writeFileSync(path.join(this.outputDir, '01-users.sql'), sqlLines.join('\n'));
  }

  async generateCategoriesSQL() {
    const sqlLines = ['-- Migrated WordPress Categories'];

    this.data.categories.forEach((category, index) => {
      const sql = `INSERT INTO categories (id, name, slug, description, sort_order) VALUES (${category.id}, '${this.escapeSql(category.name)}', '${this.escapeSql(category.slug)}', 'Migrated from WordPress', ${index + 1});`;
      sqlLines.push(sql);
    });

    fs.writeFileSync(path.join(this.outputDir, '02-categories.sql'), sqlLines.join('\n'));
  }

  async generateTagsSQL() {
    const sqlLines = ['-- Migrated WordPress Tags'];

    this.data.tags.forEach(tag => {
      const sql = `INSERT INTO tags (id, name, slug, description) VALUES (${tag.id}, '${this.escapeSql(tag.name)}', '${this.escapeSql(tag.slug)}', 'Migrated from WordPress');`;
      sqlLines.push(sql);
    });

    fs.writeFileSync(path.join(this.outputDir, '03-tags.sql'), sqlLines.join('\n'));
  }

  async generatePostsSQL() {
    const sqlLines = ['-- Migrated WordPress Posts'];

    this.data.posts.forEach(post => {
      const sql = `INSERT INTO posts (id, title, slug, content, excerpt, author_id, status, post_type, language, view_count, comment_count, meta_title, meta_description, published_at, created_at, updated_at) VALUES (${post.id}, '${this.escapeSql(post.title)}', '${this.escapeSql(post.slug)}', '${this.escapeSql(post.content)}', '${this.escapeSql(post.excerpt)}', ${post.author_id}, '${post.status}', '${post.post_type}', '${post.language}', ${post.view_count}, ${post.comment_count}, '${this.escapeSql(post.meta_title)}', '${this.escapeSql(post.meta_description)}', '${post.published_at}', '${post.created_at}', '${post.updated_at}');`;
      sqlLines.push(sql);
    });

    fs.writeFileSync(path.join(this.outputDir, '04-posts.sql'), sqlLines.join('\n'));
  }

  async generateCommentsSQL() {
    const sqlLines = ['-- Migrated WordPress Comments'];

    this.data.comments.forEach(comment => {
      const parentId = comment.parent_id ? comment.parent_id : 'NULL';
      const sql = `INSERT INTO comments (id, post_id, author_name, author_email, content, status, parent_id, created_at) VALUES (${comment.id}, ${comment.post_id}, '${this.escapeSql(comment.author_name)}', '${this.escapeSql(comment.author_email)}', '${this.escapeSql(comment.content)}', '${comment.status}', ${parentId}, '${comment.created_at}');`;
      sqlLines.push(sql);
    });

    fs.writeFileSync(path.join(this.outputDir, '05-comments.sql'), sqlLines.join('\n'));
  }

  async generateRelationshipsSQL() {
    console.log('🔗 Parsing post-category relationships...');

    // Parse wp_term_relationships to get post-category mappings
    const relationshipsMatch = this.sqlContent.match(/INSERT INTO `wp_term_relationships` VALUES\s*([\s\S]*?);/);
    if (!relationshipsMatch) return;

    const relationshipValues = relationshipsMatch[1];
    const relationshipRows = this.extractInsertValues(relationshipValues);

    const postCategoriesSQL = ['-- Post-Category Relationships'];
    const postTagsSQL = ['-- Post-Tag Relationships'];

    relationshipRows.forEach(row => {
      if (row.length >= 2) {
        const postId = parseInt(row[0]);
        const termId = parseInt(row[1]);

        // Check if this post exists in our migrated posts
        const postExists = this.data.posts.some(p => p.id === postId);
        if (!postExists) return;

        // Check if it's a category or tag
        const isCategory = this.data.categories.some(c => c.id === termId);
        const isTag = this.data.tags.some(t => t.id === termId);

        if (isCategory) {
          postCategoriesSQL.push(`INSERT INTO post_categories (post_id, category_id) VALUES (${postId}, ${termId});`);
        } else if (isTag) {
          postTagsSQL.push(`INSERT INTO post_tags (post_id, tag_id) VALUES (${postId}, ${termId});`);
        }
      }
    });

    fs.writeFileSync(path.join(this.outputDir, '06-post-categories.sql'), postCategoriesSQL.join('\n'));
    fs.writeFileSync(path.join(this.outputDir, '07-post-tags.sql'), postTagsSQL.join('\n'));
  }

  async generateMigrationScript() {
    const script = `#!/bin/bash

# WordPress to D1 Migration Execution Script
# Generated on ${new Date().toISOString()}

echo "🚀 Starting WordPress to D1 migration..."

# Execute SQL files in order
echo "📝 Migrating users..."
wrangler d1 execute ${CONFIG.DATABASE_NAME} --local --file=migration-output/01-users.sql

echo "📂 Migrating categories..."
wrangler d1 execute ${CONFIG.DATABASE_NAME} --local --file=migration-output/02-categories.sql

echo "🏷️  Migrating tags..."
wrangler d1 execute ${CONFIG.DATABASE_NAME} --local --file=migration-output/03-tags.sql

echo "📄 Migrating posts..."
wrangler d1 execute ${CONFIG.DATABASE_NAME} --local --file=migration-output/04-posts.sql

echo "💬 Migrating comments..."
wrangler d1 execute ${CONFIG.DATABASE_NAME} --local --file=migration-output/05-comments.sql

echo "🔗 Migrating post-category relationships..."
wrangler d1 execute ${CONFIG.DATABASE_NAME} --local --file=migration-output/06-post-categories.sql

echo "🔗 Migrating post-tag relationships..."
wrangler d1 execute ${CONFIG.DATABASE_NAME} --local --file=migration-output/07-post-tags.sql

echo "✅ Migration completed successfully!"
echo "📊 Migration Summary:"
echo "   Users: ${this.data.users.length}"
echo "   Categories: ${this.data.categories.length}"
echo "   Tags: ${this.data.tags.length}"
echo "   Posts: ${this.data.posts.length}"
echo "   Comments: ${this.data.comments.length}"
`;

    fs.writeFileSync(path.join(this.outputDir, 'run-migration.sh'), script);
    fs.chmodSync(path.join(this.outputDir, 'run-migration.sh'), '755');
  }

  async executeMigration() {
    console.log('🚀 Executing migration to D1...');

    try {
      // Clear existing data first
      console.log('🧹 Clearing existing data...');
      await this.clearExistingData();

      // Execute migration files
      const migrationFiles = [
        '01-users.sql',
        '02-categories.sql',
        '03-tags.sql',
        '04-posts.sql',
        '05-comments.sql',
        '06-post-categories.sql',
        '07-post-tags.sql'
      ];

      for (const file of migrationFiles) {
        const filePath = path.join(this.outputDir, file);
        if (fs.existsSync(filePath)) {
          console.log(`📝 Executing ${file}...`);
          execSync(`wrangler d1 execute ${CONFIG.DATABASE_NAME} --local --file=${filePath}`, { stdio: 'inherit' });
        }
      }

      console.log('✅ All migration files executed successfully');

    } catch (error) {
      console.error('❌ Migration execution failed:', error.message);
      throw error;
    }
  }

  async clearExistingData() {
    const clearCommands = [
      'DELETE FROM post_views;',
      'DELETE FROM post_tags;',
      'DELETE FROM post_categories;',
      'DELETE FROM post_meta;',
      'DELETE FROM comments;',
      'DELETE FROM posts;',
      'DELETE FROM tags;',
      'DELETE FROM categories;',
      // Don't delete users - we'll use INSERT OR REPLACE
      'DELETE FROM sqlite_sequence;' // Reset auto-increment
    ];

    for (const command of clearCommands) {
      try {
        execSync(`wrangler d1 execute ${CONFIG.DATABASE_NAME} --local --command="${command}"`, { stdio: 'pipe' });
      } catch (error) {
        // Ignore errors for non-existent data
        console.log(`⚠️  Warning: ${command} - ${error.message.split('\n')[0]}`);
      }
    }

    console.log('✅ Existing data cleared');
  }

  escapeSql(str) {
    if (!str) return '';
    return str.replace(/'/g, "''").replace(/\\/g, '\\\\');
  }
}

// Run migration if called directly
if (require.main === module) {
  const migrator = new WordPressMigrator();
  migrator.migrate().catch(console.error);
}

module.exports = WordPressMigrator;
