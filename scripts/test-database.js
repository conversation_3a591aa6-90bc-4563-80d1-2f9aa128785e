/**
 * Test script to verify database connection and basic operations
 */

const { blogService, settingsService, categoriesRepository, postsRepository } = require('../src/lib');

async function testDatabase() {
  console.log('🧪 Testing database connection and operations...\n');

  try {
    // Test 1: Get site settings
    console.log('1. Testing settings service...');
    const siteSettings = await settingsService.getSiteSettings();
    console.log('✅ Site settings retrieved:', siteSettings);
    console.log();

    // Test 2: Get categories
    console.log('2. Testing categories repository...');
    const categories = await categoriesRepository.getAllOrdered();
    console.log(`✅ Found ${categories.length} categories:`);
    categories.forEach(cat => console.log(`   - ${cat.name} (${cat.slug})`));
    console.log();

    // Test 3: Get category hierarchy
    console.log('3. Testing category hierarchy...');
    const hierarchy = await categoriesRepository.getCategoryHierarchy();
    console.log(`✅ Category hierarchy with ${hierarchy.length} root categories`);
    console.log();

    // Test 4: Get published posts
    console.log('4. Testing blog service - get published posts...');
    const blogPosts = await blogService.getPublishedPosts(1, 5);
    console.log(`✅ Found ${blogPosts.posts.length} published posts (total: ${blogPosts.total})`);
    console.log();

    // Test 5: Test individual setting
    console.log('5. Testing individual setting retrieval...');
    const siteTitle = await settingsService.get('site_title', 'Default Title');
    console.log(`✅ Site title: ${siteTitle}`);
    console.log();

    // Test 6: Test post count
    console.log('6. Testing post count...');
    const postCount = await postsRepository.count();
    console.log(`✅ Total posts in database: ${postCount}`);
    console.log();

    console.log('🎉 All database tests passed successfully!');
    
  } catch (error) {
    console.error('❌ Database test failed:', error);
  }
}

// Run the test
testDatabase().catch(console.error);
