/**
 * Test page to verify database connection
 */

import { settingsService, categoriesRepository, postService } from '@/lib';

export default async function TestDatabasePage() {
  let testResults: any = {};
  let error: string | null = null;

  try {
    // Test database operations
    const [siteSettings, categoriesCount, blogPosts, siteTitle] = await Promise.all([
      settingsService.getSiteSettings(),
      categoriesRepository.count(),
      postService.getPosts({ status: 'published', offset: 0, limit: 5 }),
      settingsService.get('site_title', 'Default Title'),
    ]);

    testResults = {
      siteSettings,
      categoriesCount,
      blogPostsCount: blogPosts.posts.length,
      totalPosts: blogPosts.total,
      siteTitle,
    };

  } catch (err) {
    error = err instanceof Error ? err.message : 'Unknown error occurred';
    console.error('Database test error:', err);
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-6">Database Connection Test</h1>
      
      {error ? (
        <div className="bg-red-50 border border-red-200 rounded-lg p-6">
          <h2 className="text-xl font-semibold text-red-800 mb-4">❌ Test Failed</h2>
          <p className="text-red-700 mb-4">{error}</p>

        </div>
      ) : (
        <div className="bg-green-50 border border-green-200 rounded-lg p-6">
          <h2 className="text-xl font-semibold text-green-800 mb-4">✅ Database Tests Passed</h2>
          
          <div className="space-y-4">
            <div>
              <h3 className="font-semibold text-gray-800">Site Settings:</h3>
              <pre className="bg-gray-100 p-3 rounded text-sm overflow-x-auto">
                {JSON.stringify(testResults.siteSettings, null, 2)}
              </pre>
            </div>

            <div>
              <h3 className="font-semibold text-gray-800">Database Stats:</h3>
              <ul className="list-disc list-inside text-gray-700">
                <li>Categories: {testResults.categoriesCount}</li>
                <li>Published Posts: {testResults.blogPostsCount}</li>
                <li>Total Posts: {testResults.totalPosts}</li>
                <li>Site Title: {testResults.siteTitle}</li>
              </ul>
            </div>
          </div>


        </div>
      )}

      <div className="mt-8">
        <h2 className="text-xl font-semibold mb-4">Database Information</h2>
        <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
          <ul className="space-y-2 text-gray-700">
            <li><strong>Database ID:</strong> 38d675e4-df5b-4c8a-b6f2-490996d8dd89</li>
            <li><strong>Database Type:</strong> Cloudflare D1</li>
            <li><strong>Environment:</strong> {process.env.NODE_ENV || 'development'}</li>
            <li><strong>Tables Created:</strong> ✅ All schema tables initialized</li>
            <li><strong>Default Data:</strong> ✅ Categories and settings populated</li>
          </ul>
        </div>
      </div>

      <div className="mt-6">
        <a 
          href="/api/test-db" 
          className="inline-block bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors"
          target="_blank"
          rel="noopener noreferrer"
        >
          View API Test Results
        </a>
      </div>
    </div>
  );
}
