/**
 * API route to test database connection
 */

import { NextResponse } from 'next/server';
import { settingsService, categoriesRepository, postService } from '@/lib';

export async function GET() {
  try {
    console.log('🧪 Testing database connection...');

    // Test 1: Get site settings
    const siteSettings = await settingsService.getSiteSettings();
    
    // Test 2: Get categories count
    const categoriesCount = await categoriesRepository.count();
    
    // Test 3: Get published posts
    const blogPosts = await postService.getPosts({ status: 'published', page: 1, limit: 5 });

    // Test 4: Get individual setting
    const siteTitle = await settingsService.get('site_title', 'Default Title');

    const testResults = {
      success: true,
      timestamp: new Date().toISOString(),
      tests: {
        siteSettings: {
          success: true,
          data: siteSettings,
        },
        categoriesCount: {
          success: true,
          count: categoriesCount,
        },
        blogPosts: {
          success: true,
          postsFound: blogPosts.posts.length,
          totalPosts: blogPosts.total,
        },
        siteTitle: {
          success: true,
          title: siteTitle,
        },
      },
      message: 'All database tests passed successfully!',
    };

    return NextResponse.json(testResults);

  } catch (error) {
    console.error('❌ Database test failed:', error);

    const errorResponse = {
      success: false,
      timestamp: new Date().toISOString(),
      error: {
        message: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined,
      },
      note: 'Database connection failed. Please check Cloudflare D1 configuration.',
    };

    return NextResponse.json(errorResponse, { status: 500 });
  }
}
