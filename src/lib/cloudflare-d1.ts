/**
 * Cloudflare D1 Database Service
 * Uses wrangler CLI to interact with D1 database in development
 */

import { execSync } from 'child_process';
import { DatabaseConnection } from './database';

export class WranglerD1Database implements DatabaseConnection {
  private databaseName: string;

  constructor(databaseName: string = 'blog-database') {
    this.databaseName = databaseName;
  }

  prepare(query: string) {
    return {
      bind: (...params: any[]) => ({
        all: async () => {
          const results = await this.executeQuery(query, params);
          return { results, success: true, meta: { changes: results.length } };
        },
        first: async () => {
          const results = await this.executeQuery(query, params);
          return results[0] || null;
        },
        run: async () => {
          const results = await this.executeQuery(query, params);
          return {
            success: true,
            meta: {
              changes: results.length,
              last_row_id: 0,
            }
          };
        }
      }),
      all: async () => {
        const results = await this.executeQuery(query, []);
        return { results, success: true, meta: { changes: results.length } };
      },
      first: async () => {
        const results = await this.executeQuery(query, []);
        return results[0] || null;
      },
      run: async () => {
        const results = await this.executeQuery(query, []);
        return {
          success: true,
          meta: {
            changes: results.length,
            last_row_id: 0,
          }
        };
      }
    };
  }

  private async executeQuery(sql: string, params: any[] = []): Promise<any[]> {
    try {
      console.log('🔍 Executing D1 query:', sql);
      if (params.length > 0) {
        console.log('📝 Query params:', params);
      }

      // Replace parameter placeholders with actual values
      let processedSql = sql;
      params.forEach((param, index) => {
        const placeholder = '?';
        const value = typeof param === 'string' ? `'${param.replace(/'/g, "''")}'` : param;
        processedSql = processedSql.replace(placeholder, value);
      });

      // Escape quotes for shell command
      const escapedSql = processedSql.replace(/"/g, '\\"');
      
      // Execute wrangler command
      const command = `npx wrangler d1 execute ${this.databaseName} --command "${escapedSql}" --json`;
      
      console.log('🚀 Executing command:', command);
      
      const output = execSync(command, { 
        encoding: 'utf8',
        stdio: ['pipe', 'pipe', 'pipe']
      });

      // Parse JSON output
      try {
        const lines = output.split('\n').filter(line => line.trim());

        // Look for JSON array or object
        for (const line of lines) {
          if (line.startsWith('[') || line.startsWith('{')) {
            try {
              const result = JSON.parse(line);
              console.log('✅ Query result:', result);
              return Array.isArray(result) ? result : [result];
            } catch (e) {
              continue;
            }
          }
        }

        // If no JSON found, try to extract from wrangler output format
        console.warn('⚠️  Could not parse JSON output, checking for data...');

        // Check if query was successful but returned no data
        if (output.includes('Executed') || output.includes('Success')) {
          console.log('✅ Query executed successfully (no data returned)');
          return [];
        }

      } catch (parseError) {
        console.warn('⚠️  Parse error:', parseError);
      }

      console.log('✅ Query executed successfully');
      return [];

    } catch (error) {
      console.error('❌ D1 query failed:', error);
      
      // If it's a "no such table" error, return empty results
      if (error instanceof Error && error.message.includes('no such table')) {
        console.log('📝 Table does not exist, returning empty results');
        return [];
      }
      
      throw error;
    }
  }
}

// Export a singleton instance
export const wranglerD1 = new WranglerD1Database();
