/**
 * Database configuration and connection utilities for Cloudflare D1
 */

// Database configuration
export const DATABASE_CONFIG = {
  // This will be set via environment variables in production
  DATABASE_ID: process.env.DATABASE_ID || '38d675e4-df5b-4c8a-b6f2-490996d8dd89',
  // For development, we'll use Cloudflare D1 via wrangler
  IS_DEVELOPMENT: process.env.NODE_ENV === 'development',
} as const;

// Type definitions for our database schema
export interface User {
  id: number;
  username: string;
  email: string;
  password_hash: string;
  display_name: string;
  bio?: string;
  avatar_url?: string;
  role: 'subscriber' | 'author' | 'editor' | 'admin';
  status: 'active' | 'inactive' | 'banned';
  email_verified: boolean;
  created_at: string;
  updated_at: string;
}

export interface Category {
  id: number;
  name: string;
  slug: string;
  description?: string;
  parent_id?: number;
  sort_order: number;
  created_at: string;
  updated_at: string;
}

export interface Tag {
  id: number;
  name: string;
  slug: string;
  description?: string;
  created_at: string;
}

export interface Post {
  id: number;
  title: string;
  slug: string;
  content: string;
  excerpt?: string;
  author_id: number;
  status: 'draft' | 'published' | 'private' | 'trash';
  post_type: 'post' | 'page';
  featured_image_url?: string;
  meta_title?: string;
  meta_description?: string;
  language: 'en' | 'hi';
  view_count: number;
  comment_count: number;
  published_at?: string;
  created_at: string;
  updated_at: string;
}

export interface Comment {
  id: number;
  post_id: number;
  parent_id?: number;
  author_name: string;
  author_email: string;
  author_url?: string;
  author_ip?: string;
  content: string;
  status: 'pending' | 'approved' | 'spam' | 'trash';
  user_agent?: string;
  created_at: string;
  updated_at: string;
}

export interface Media {
  id: number;
  filename: string;
  original_filename: string;
  file_path: string;
  file_size: number;
  mime_type: string;
  width?: number;
  height?: number;
  alt_text?: string;
  caption?: string;
  uploaded_by?: number;
  created_at: string;
}

export interface Setting {
  key: string;
  value?: string;
  autoload: boolean;
  updated_at: string;
}

// Database connection interface
export interface DatabaseConnection {
  prepare(query: string): {
    bind(...params: any[]): {
      all(): Promise<{ results: any[]; success: boolean; meta: any }>;
      first(): Promise<any>;
      run(): Promise<{ success: boolean; meta: any }>;
    };
  };
}



import { wranglerD1 } from './cloudflare-d1';

// Get database connection
export function getDatabase(): DatabaseConnection {
  // In production, this will be provided by Cloudflare Workers runtime
  if (typeof globalThis !== 'undefined' && (globalThis as any).DB) {
    return (globalThis as any).DB;
  }

  // For development, always use Cloudflare D1 via wrangler
  if (typeof process !== 'undefined' && process.env.NODE_ENV === 'development') {
    console.log('🔗 Connecting to Cloudflare D1 via wrangler...');
    return wranglerD1;
  }

  throw new Error('Database not available. Make sure D1 database is properly configured.');
}

// Database utility functions
export class DatabaseUtils {
  private db: DatabaseConnection;

  constructor() {
    this.db = getDatabase();
  }

  // Execute a query with parameters
  async query(sql: string, params: any[] = []) {
    try {
      const stmt = this.db.prepare(sql);
      const result = await stmt.bind(...params).all();
      return result;
    } catch (error) {
      console.error('Database query error:', error);
      throw error;
    }
  }

  // Execute a query and return first result
  async queryFirst(sql: string, params: any[] = []) {
    try {
      const stmt = this.db.prepare(sql);
      const result = await stmt.bind(...params).first();
      return result;
    } catch (error) {
      console.error('Database query error:', error);
      throw error;
    }
  }

  // Execute a query that modifies data
  async execute(sql: string, params: any[] = []) {
    try {
      const stmt = this.db.prepare(sql);
      const result = await stmt.bind(...params).run();
      return result;
    } catch (error) {
      console.error('Database execute error:', error);
      throw error;
    }
  }
}

// Export a singleton instance
export const db = new DatabaseUtils();
