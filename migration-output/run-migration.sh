#!/bin/bash

# WordPress to D1 Migration Execution Script
# Generated on 2025-07-30T07:10:49.323Z

echo "🚀 Starting WordPress to D1 migration..."

# Execute SQL files in order
echo "📝 Migrating users..."
wrangler d1 execute blog-database --local --file=migration-output/01-users.sql

echo "📂 Migrating categories..."
wrangler d1 execute blog-database --local --file=migration-output/02-categories.sql

echo "🏷️  Migrating tags..."
wrangler d1 execute blog-database --local --file=migration-output/03-tags.sql

echo "📄 Migrating posts..."
wrangler d1 execute blog-database --local --file=migration-output/04-posts.sql

echo "💬 Migrating comments..."
wrangler d1 execute blog-database --local --file=migration-output/05-comments.sql

echo "🔗 Migrating post-category relationships..."
wrangler d1 execute blog-database --local --file=migration-output/06-post-categories.sql

echo "🔗 Migrating post-tag relationships..."
wrangler d1 execute blog-database --local --file=migration-output/07-post-tags.sql

echo "✅ Migration completed successfully!"
echo "📊 Migration Summary:"
echo "   Users: 3"
echo "   Categories: 19"
echo "   Tags: 192"
echo "   Posts: 0"
echo "   Comments: 0"
